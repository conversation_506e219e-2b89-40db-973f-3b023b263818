from setuptools import setup, find_packages

setup(
    name='your_app_name',           # Replace with a meaningful name
    version='0.1.0',
    packages=find_packages(),       # Automatically find packages like 'app', 'app.submodule', etc.
    install_requires=[
        # Add dependencies here, for example:
        'fastapi', 'uvicorn', 'requests'
    ],
    include_package_data=True,
    description='Your app description',
    author='Your Name',
    author_email='<EMAIL>',
    classifiers=[
        'Programming Language :: Python :: 3',
        'Operating System :: OS Independent',
    ],
    python_requires='>=3.7',
)
