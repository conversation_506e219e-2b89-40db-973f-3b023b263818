#!/usr/bin/env python3
"""
Setup script for Multi-Tenant System Backend

This setup.py provides backward compatibility and additional packaging features
alongside the modern pyproject.toml configuration.
"""

import os
import sys
from setuptools import setup, find_packages

# Ensure we're in the right directory
here = os.path.abspath(os.path.dirname(__file__))

# Read the README file for long description
def read_readme():
    readme_path = os.path.join(here, 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Multi-Tenant System Backend - A FastAPI-based multi-tenant management system"

# Read requirements from requirements.txt
def read_requirements():
    requirements_path = os.path.join(here, 'requirements.txt')
    requirements = []
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    requirements.append(line)
    return requirements

# Read version from app/__init__.py or set default
def get_version():
    version_file = os.path.join(here, 'app', '__init__.py')
    if os.path.exists(version_file):
        with open(version_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.startswith('__version__'):
                    return line.split('=')[1].strip().strip('"').strip("'")
    return "0.1.0"

# Development requirements
dev_requirements = [
    'pytest>=7.0.0',
    'pytest-asyncio>=0.21.0',
    'pytest-cov>=4.0.0',
    'black>=23.0.0',
    'flake8>=6.0.0',
    'mypy>=1.0.0',
    'pre-commit>=3.0.0',
    'httpx>=0.24.0',  # For testing FastAPI
]

# Production requirements (additional to base requirements)
prod_requirements = [
    'gunicorn>=21.0.0',
    'uvicorn[standard]>=0.23.0',
]

setup(
    # Basic package information
    name="multitenant-system-backend",
    version=get_version(),
    description="Multi-Tenant System Backend - FastAPI-based tenant management system",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    
    # Author information
    author="NextAI Team",
    author_email="<EMAIL>",
    url="https://github.com/nextai/multitenant-system-backend",
    
    # Package discovery
    packages=find_packages(exclude=['tests', 'tests.*', 'docs', 'docs.*']),
    include_package_data=True,
    
    # Python version requirement
    python_requires=">=3.11",
    
    # Dependencies
    install_requires=read_requirements(),
    
    # Optional dependencies
    extras_require={
        'dev': dev_requirements,
        'prod': prod_requirements,
        'all': dev_requirements + prod_requirements,
    },
    
    # Entry points for command-line scripts
    entry_points={
        'console_scripts': [
            'multitenant-server=main:app',
            'multitenant-dev=scripts.dev_server:main',
        ],
    },
    
    # Package data
    package_data={
        'app': [
            'templates/*.html',
            'static/*',
            'config/*.json',
            'config/*.yaml',
        ],
    },
    
    # Classifiers for PyPI
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Framework :: FastAPI",
        "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
        "Topic :: Software Development :: Libraries :: Application Frameworks",
        "Topic :: System :: Systems Administration",
    ],
    
    # Keywords for discovery
    keywords="fastapi multitenant saas backend api mongodb jwt authentication",
    
    # Project URLs
    project_urls={
        "Bug Reports": "https://github.com/nextai/multitenant-system-backend/issues",
        "Source": "https://github.com/nextai/multitenant-system-backend",
        "Documentation": "https://github.com/nextai/multitenant-system-backend/blob/main/README.md",
    },
    
    # Zip safety
    zip_safe=False,
    
    # Platform compatibility
    platforms=['any'],
)
