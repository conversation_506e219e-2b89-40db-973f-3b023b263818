#!/usr/bin/env python3
"""
Test script to verify the package installation and egg-info creation
"""

import os
import sys
import importlib.util

def test_package_installation():
    """Test if package is properly installed"""
    print("🧪 Testing Package Installation")
    print("=" * 40)
    
    # Test 1: Check if app module can be imported
    try:
        import app
        print("✅ App module imported successfully")
        print(f"   Version: {getattr(app, '__version__', 'Unknown')}")
        print(f"   Author: {getattr(app, '__author__', 'Unknown')}")
    except ImportError as e:
        print(f"❌ Failed to import app module: {e}")
        return False
    
    # Test 2: Check if main module works
    try:
        import main
        print("✅ Main module imported successfully")
        print(f"   App title: {main.app.title}")
    except ImportError as e:
        print(f"❌ Failed to import main module: {e}")
        return False
    
    # Test 3: Check egg-info directory
    egg_info_dirs = [d for d in os.listdir('.') if d.endswith('.egg-info')]
    if egg_info_dirs:
        egg_info_dir = egg_info_dirs[0]
        print(f"✅ Egg-info directory found: {egg_info_dir}")
        
        # Check important files in egg-info
        egg_info_path = os.path.join('.', egg_info_dir)
        important_files = ['PKG-INFO', 'SOURCES.txt', 'requires.txt', 'top_level.txt']
        
        for file in important_files:
            file_path = os.path.join(egg_info_path, file)
            if os.path.exists(file_path):
                print(f"   ✅ {file}")
                
                # Show content of some files
                if file == 'top_level.txt':
                    with open(file_path, 'r') as f:
                        content = f.read().strip()
                        print(f"      Top-level packages: {content}")
                        
                elif file == 'requires.txt':
                    with open(file_path, 'r') as f:
                        lines = f.readlines()[:5]  # Show first 5 requirements
                        print(f"      Dependencies: {len(f.readlines())} total")
                        for line in lines:
                            print(f"        - {line.strip()}")
            else:
                print(f"   ❌ {file} missing")
    else:
        print("❌ No egg-info directory found")
        return False
    
    # Test 4: Check if FastAPI app is working
    try:
        from fastapi.testclient import TestClient
        client = TestClient(main.app)
        
        # Test health endpoint if it exists
        response = client.get("/")
        print(f"✅ FastAPI app responds: {response.status_code}")
        
    except Exception as e:
        print(f"⚠️  FastAPI test client error: {e}")
    
    # Test 5: Check environment setup
    if os.path.exists('.env'):
        print("✅ Environment file (.env) exists")
    else:
        print("⚠️  No .env file found")
    
    # Test 6: Check project directories
    project_dirs = ['logs', 'data', 'uploads']
    for dir_name in project_dirs:
        if os.path.exists(dir_name):
            print(f"✅ Directory {dir_name}/ exists")
        else:
            print(f"⚠️  Directory {dir_name}/ not found")
    
    print("\n🎉 Package installation test completed!")
    return True

def show_package_info():
    """Show detailed package information"""
    print("\n📦 Package Information")
    print("=" * 40)
    
    # Show egg-info contents
    egg_info_dirs = [d for d in os.listdir('.') if d.endswith('.egg-info')]
    if egg_info_dirs:
        egg_info_dir = egg_info_dirs[0]
        egg_info_path = os.path.join('.', egg_info_dir)
        
        # Show PKG-INFO
        pkg_info_path = os.path.join(egg_info_path, 'PKG-INFO')
        if os.path.exists(pkg_info_path):
            print("📄 PKG-INFO:")
            with open(pkg_info_path, 'r') as f:
                lines = f.readlines()[:15]  # Show first 15 lines
                for line in lines:
                    print(f"   {line.rstrip()}")
        
        # Show entry points if they exist
        entry_points_path = os.path.join(egg_info_path, 'entry_points.txt')
        if os.path.exists(entry_points_path):
            print("\n🔧 Entry Points:")
            with open(entry_points_path, 'r') as f:
                content = f.read()
                print(f"   {content}")

def main():
    """Main test function"""
    print("🧪 Multi-Tenant System Backend - Package Test")
    print("=" * 60)
    
    # Run tests
    success = test_package_installation()
    
    if success:
        show_package_info()
        print("\n✅ All tests passed! Package is working correctly.")
        print("\n🚀 Ready to use:")
        print("   uvicorn main:app --reload")
        print("   http://localhost:8000/docs")
    else:
        print("\n❌ Some tests failed. Check installation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
