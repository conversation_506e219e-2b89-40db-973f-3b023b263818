# Installation Guide

## 📦 Package Installation

### Method 1: Development Installation (Recommended for development)

```bash
# Clone the repository
git clone <repository-url>
cd multitenant_system_backend

# Install in development mode with all dependencies
pip install -e .[all]
```

### Method 2: Production Installation

```bash
# Install from source
pip install .

# Or install with production dependencies
pip install .[prod]
```

### Method 3: Using pip from Git (if hosted on Git)

```bash
# Install directly from Git repository
pip install git+https://github.com/nextai/multitenant-system-backend.git

# Install specific branch or tag
pip install git+https://github.com/nextai/multitenant-system-backend.git@main
```

## 🔧 Setup

### 1. Environment Configuration

```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### 2. Required Environment Variables

```env
# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017/
DATABASE_NAME=multi_tenant_admin

# Optional: JWT Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
```

## 🚀 Running the Application

### Development Mode

```bash
# Using the development script
multitenant-dev

# Or using uvicorn directly
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Production Mode

```bash
# Using gunicorn (recommended for production)
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# Or using uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000
```

### Docker Mode

```bash
# Build and run with Docker Compose
docker-compose up --build

# Run in detached mode
docker-compose up -d --build
```

## 📚 API Access

Once running, access:

- **API**: http://localhost:8000 (or 8207 with Docker)
- **Interactive API Docs**: http://localhost:8000/docs
- **Alternative API Docs**: http://localhost:8000/redoc

## 🧪 Development Setup

### Install Development Dependencies

```bash
# Install with development dependencies
pip install -e .[dev]

# Install pre-commit hooks
pre-commit install
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_tenant.py
```

### Code Quality

```bash
# Format code
black app/

# Check code style
flake8 app/

# Type checking
mypy app/
```

## 🔍 Package Information

### Available Console Scripts

After installation, these commands are available:

- `multitenant-server`: Start the server (points to main:app)
- `multitenant-dev`: Start development server with hot-reload

### Package Structure

```
multitenant-system-backend/
├── app/                    # Main application package
│   ├── core/              # Core functionality
│   ├── models/            # Data models
│   ├── routers/           # API routes
│   ├── schemas/           # Pydantic schemas
│   ├── tenant/            # Tenant management
│   └── utils.py           # Utility functions
├── scripts/               # Helper scripts
├── tests/                 # Test files
├── main.py               # Application entry point
├── setup.py              # Package setup
├── pyproject.toml        # Modern Python packaging
└── requirements.txt      # Dependencies
```

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**: Make sure you installed the package with `pip install -e .`
2. **MongoDB Connection**: Verify your `MONGO_URI` in `.env` file
3. **Port Conflicts**: Change the port in your run command if 8000 is occupied
4. **Permission Errors**: Make sure you have write permissions in the project directory

### Getting Help

- Check the [API Documentation](API_DOCUMENTATION.md)
- Review the [Implementation Guide](README_IMPLEMENTATION.md)
- Check existing issues in the repository
- Create a new issue with detailed error information
