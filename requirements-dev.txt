# Development Dependencies
# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
httpx>=0.24.0  # For testing FastAPI endpoints

# Code Quality
black>=23.7.0
flake8>=6.0.0
isort>=5.12.0
mypy>=1.5.0
pre-commit>=3.3.0

# Development Tools
uvicorn[standard]>=0.23.0
watchfiles>=0.19.0  # For file watching
python-dotenv>=1.0.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.1.0
mkdocs-swagger-ui-tag>=0.6.0

# Debugging
ipdb>=0.13.0
rich>=13.4.0  # Better console output

# Database Tools
pymongo[srv]>=4.5.0  # Full MongoDB support

# Security Testing
bandit>=1.7.0
safety>=2.3.0

# Performance Testing
locust>=2.15.0

# API Testing
postman-collection>=0.0.1
