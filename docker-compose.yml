version: '3.8'

services:
  # FastAPI Application
  multitenant-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: multitenant-backend
    ports:
      - "8207:8000"  # External port 8207 maps to internal port 8000
    environment:
      - MONGO_URI=mongodb://mongodb:27017/
      - DATABASE_NAME=multi_tenant_admin
      - PYTHONPATH=/app
    volumes:
      - .:/app
      - /app/__pycache__
    depends_on:
      - mongodb
    networks:
      - multitenant-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: multitenant-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=multi_tenant_admin
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - multitenant-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Express (Optional - for database management)
  mongo-express:
    image: mongo-express:1.0.0
    container_name: multitenant-mongo-express
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=admin
      - ME_CONFIG_MONGODB_ADMINPASSWORD=password123
      - ME_CONFIG_MONGODB_URL=*****************************************/
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin123
    depends_on:
      - mongodb
    networks:
      - multitenant-network
    restart: unless-stopped

volumes:
  mongodb_data:
    driver: local

networks:
  multitenant-network:
    driver: bridge
