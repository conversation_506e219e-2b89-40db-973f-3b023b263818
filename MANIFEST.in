# Include documentation files
include README.md
include README_IMPLEMENTATION.md
include API_DOCUMENTATION.md
include FRONTEND_API_DOCUMENTATION.md
include LICENSE

# Include configuration files
include requirements.txt
include pyproject.toml
include .env.example

# Include Docker files
include Dockerfile
include docker-compose.yml

# Include all Python files
recursive-include app *.py

# Include any template or static files
recursive-include app/templates *.html
recursive-include app/static *
recursive-include app/config *.json *.yaml *.yml

# Include test files
recursive-include tests *.py

# Include scripts
recursive-include scripts *.py *.sh

# Exclude compiled Python files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .pytest_cache
global-exclude *.so

# Exclude development files
exclude .gitignore
exclude .pre-commit-config.yaml
exclude tox.ini
exclude .coverage
exclude .env

# Exclude build artifacts
global-exclude *.egg-info
global-exclude build
global-exclude dist
