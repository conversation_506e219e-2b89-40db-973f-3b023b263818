# Frontend API Documentation

## Base URL
```
http://127.0.0.1:8000
```

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <access_token>
```

---

## 🔐 Authentication Endpoints

### POST /auth/login
**Description**: Authenticate user and get access token for admin system

**Method**: `POST`
**Content-Type**: `application/x-www-form-urlencoded`

**Request Parameters**:
- `username` (string, required): Admin username
- `password` (string, required): Admin password

**cURL Example**:
```bash
curl -X 'POST' \
  'http://127.0.0.1:8000/create-tenent?tenant_name=test&tenant_slug=test&product=685d2c9bb23fc531edf6dd0c&username=admin' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.\
```

**Response** (200 OK):
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInJvbGUiOiJhZG1pbiIsImV4cCI6MTczNTU2NzIwMH0.example_signature",
  "token_type": "bearer",
  "user": {
    "id": "675f1234567890abcdef1234",
    "username": "admin",
    "role": "admin"
  }
}
```

**Error Responses**:
- `401 Unauthorized`: Invalid credentials
  ```json
  {
    "detail": "Incorrect username or password"
  }
  ```
- `500 Internal Server Error`: Authentication error
  ```json
  {
    "detail": "Authentication error: <error_message>"
  }
  ```

---

## 🏢 Tenant Management Endpoints

### GET /tenants/list
**Description**: Get list of all tenants (Admin only)

**Method**: `GET`
**Authentication**: Required (Bearer token)

**cURL Example**:
```bash
curl -X GET "http://127.0.0.1:8000/tenants/list" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response** (200 OK):
```json
{
  "success": true,
  "tenants": [
    {
      "id": "675f1234567890abcdef1234",
      "name": "Company A",
      "slug": "company-a",
      "database_name": "eko_company-a_db"
    },
    {
      "id": "675f1234567890abcdef5678",
      "name": "Company B",
      "slug": "company-b",
      "database_name": "eko_company-b_db"
    }
  ],
  "total": 2
}
```

**Error Responses**:
- `401 Unauthorized`: Invalid or missing token
  ```json
  {
    "detail": "Not authenticated"
  }
  ```
- `403 Forbidden`: Not admin role
  ```json
  {
    "detail": "Admin access required"
  }
  ```
- `500 Internal Server Error`: Server error
  ```json
  {
    "detail": "Error fetching tenants: <error_message>"
  }
  ```

### GET /tenants/{tenant_id}
**Description**: Get specific tenant details by ID

**Method**: `GET`
**Authentication**: Required (Bearer token)

**Path Parameters**:
- `tenant_id` (string, required): The tenant ObjectId

**cURL Example**:
```bash
curl -X GET "http://127.0.0.1:8000/tenants/675f1234567890abcdef1234" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response** (200 OK):
```json
{
  "success": true,
  "tenant": {
    "id": "675f1234567890abcdef1234",
    "name": "Company A",
    "slug": "company-a",
    "database_name": "eko_company-a_db"
  }
}
```

**Error Responses**:
- `400 Bad Request`: Invalid tenant ID format
  ```json
  {
    "detail": "Invalid tenant ID format"
  }
  ```
- `401 Unauthorized`: Invalid or missing token
  ```json
  {
    "detail": "Not authenticated"
  }
  ```
- `403 Forbidden`: Not admin role
  ```json
  {
    "detail": "Admin access required"
  }
  ```
- `404 Not Found`: Tenant not found
  ```json
  {
    "detail": "Tenant not found"
  }
  ```
- `500 Internal Server Error`: Server error
  ```json
  {
    "detail": "Error fetching tenant: <error_message>"
  }
  ```

### POST /create-tenent
**Description**: Create a new tenant with default configuration

**Method**: `POST`
**Content-Type**: `application/json`
**Authentication**: Required (Bearer token)

**Request Body Parameters**:
- `tenant_name` (string, required): Display name for the tenant
- `tenant_slug` (string, required): URL-friendly identifier for the tenant
- `product` (string, required): Product ObjectId to associate with the tenant
- `username` (string, optional): Default admin username (defaults to "admin")

**cURL Example**:
```bash
curl -X POST "http://127.0.0.1:8000/create-tenent" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_name": "New Company",
    "tenant_slug": "new-company",
    "product": "675f1234567890abcdef1234",
    "username": "admin"
  }'
```

**Response** (200 OK):
```json
{
  "success": true,
  "message": "Tenant created successfully",
  "tenant_id": "675f9876543210fedcba9876",
  "tenant_slug": "new-company",
  "database_name": "eko_new-company_db",
  "invitation_link": "https://eko.nextai.asia/invitation?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInJvbGUiOiJhZG1pbiJ9.signature&username=admin&role=admin"
}
```

**Error Responses**:
- `400 Bad Request`: Invalid request data
  ```json
  {
    "detail": "Tenant name and slug are required"
  }
  ```
  ```json
  {
    "detail": "Invalid product ID format"
  }
  ```
- `401 Unauthorized`: Invalid or missing token
  ```json
  {
    "detail": "Not authenticated"
  }
  ```
- `403 Forbidden`: Not admin role
  ```json
  {
    "detail": "Admin access required"
  }
  ```
- `404 Not Found`: Product not found
  ```json
  {
    "detail": "Product with ID 675f1234567890abcdef1234 not found"
  }
  ```
- `500 Internal Server Error`: Server error
  ```json
  {
    "detail": "Failed to create tenant: <error_message>"
  }
  ```

---

## 👤 User Management Endpoints

### POST /create_user
**Description**: Create a new user in the admin system

**Method**: `POST`
**Content-Type**: `application/json`
**Authentication**: Required (Bearer token, Admin only)

**Request Body Parameters**:
- `username` (string, required): Unique username for the user
- `password` (string, required): Password for the user
- `role` (string, required): User role ("admin", "supervisor", or "user")

**cURL Example**:
```bash
curl -X POST "http://127.0.0.1:8000/create_user" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "securepassword123",
    "role": "supervisor"
  }'
```

**Response** (200 OK):
```json
{
  "success": true,
  "message": "User created successfully",
  "user_id": "675f9876543210fedcba5432",
  "username": "newuser",
  "role": "supervisor"
}
```

**Error Responses**:
- `400 Bad Request`: User already exists or invalid role
  ```json
  {
    "detail": "User with this username already exists"
  }
  ```
  ```json
  {
    "detail": "Invalid role. Only 'admin', 'supervisor', and 'user' roles are allowed in this system"
  }
  ```
- `401 Unauthorized`: Invalid or missing token
  ```json
  {
    "detail": "Not authenticated"
  }
  ```
- `403 Forbidden`: Not admin role
  ```json
  {
    "detail": "Admin access required"
  }
  ```
- `500 Internal Server Error`: Server error
  ```json
  {
    "detail": "Error creating user: <error_message>"
  }
  ```

### GET /verify_token
**Description**: Verify if the current token is valid

**Method**: `GET`
**Authentication**: Required (Bearer token)

**cURL Example**:
```bash
curl -X GET "http://127.0.0.1:8000/verify_token" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response** (200 OK):
```json
true
```

**Error Responses**:
- `401 Unauthorized`: Invalid or expired token
  ```json
  {
    "detail": "Could not validate credentials"
  }
  ```

---

## 📱 Frontend Implementation Guide

### 1. Login Screen Implementation

```javascript
// Login API call using form data (as required by the API)
const login = async (username, password) => {
  try {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);

    const response = await fetch('/auth/login', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const data = await response.json();
      // Store token in localStorage or secure storage
      localStorage.setItem('access_token', data.access_token);
      localStorage.setItem('user', JSON.stringify(data.user));
      return data;
    } else {
      const error = await response.json();
      throw new Error(error.detail || 'Login failed');
    }
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

// Alternative using URLSearchParams
const loginAlt = async (username, password) => {
  try {
    const params = new URLSearchParams();
    params.append('username', username);
    params.append('password', password);

    const response = await fetch('/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: params
    });

    if (response.ok) {
      const data = await response.json();
      localStorage.setItem('access_token', data.access_token);
      localStorage.setItem('user', JSON.stringify(data.user));
      return data;
    } else {
      const error = await response.json();
      throw new Error(error.detail || 'Login failed');
    }
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};
```

### 2. Tenant List Page Implementation

```javascript
// Fetch tenants list
const fetchTenants = async () => {
  try {
    const token = localStorage.getItem('access_token');
    const response = await fetch('/tenants/list', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      return data; // Returns { success: true, tenants: [...], total: number }
    } else {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to fetch tenants');
    }
  } catch (error) {
    console.error('Fetch tenants error:', error);
    throw error;
  }
};

// Tenant list component example
const TenantList = () => {
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchTenants()
      .then(data => {
        setTenants(data.tenants);
        setLoading(false);
      })
      .catch(err => {
        setError(err.message);
        setLoading(false);
      });
  }, []);

  if (loading) return <div>Loading tenants...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>Tenants ({tenants.length})</h2>
      {tenants.map(tenant => (
        <div key={tenant.id} onClick={() => selectTenant(tenant.id)}>
          <h3>{tenant.name}</h3>
          <p>Slug: {tenant.slug}</p>
          <p>Database: {tenant.database_name}</p>
        </div>
      ))}
    </div>
  );
};
```

### 3. Tenant Selection Implementation

```javascript
// Get specific tenant details
const getTenantDetails = async (tenantId) => {
  try {
    const token = localStorage.getItem('access_token');
    const response = await fetch(`/tenants/${tenantId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      return data.tenant;
    } else {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to fetch tenant details');
    }
  } catch (error) {
    console.error('Fetch tenant details error:', error);
    throw error;
  }
};

// Handle tenant selection
const selectTenant = async (tenantId) => {
  try {
    const tenantDetails = await getTenantDetails(tenantId);
    // Store selected tenant or navigate to tenant dashboard
    localStorage.setItem('selected_tenant', JSON.stringify(tenantDetails));
    // Navigate to tenant dashboard
    window.location.href = `/dashboard/${tenantDetails.slug}`;
  } catch (error) {
    console.error('Tenant selection error:', error);
    alert('Failed to select tenant: ' + error.message);
  }
};
```

### 4. Create User Implementation

```javascript
// Create new user
const createUser = async (userData) => {
  try {
    const token = localStorage.getItem('access_token');
    const response = await fetch('/create_user', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });

    if (response.ok) {
      const data = await response.json();
      return data;
    } else {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to create user');
    }
  } catch (error) {
    console.error('Create user error:', error);
    throw error;
  }
};

// Create user form component
const CreateUserForm = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    role: 'user'
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const result = await createUser(formData);
      alert(`User ${result.username} created successfully!`);
      setFormData({ username: '', password: '', role: 'user' });
    } catch (error) {
      alert('Error creating user: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Username"
        value={formData.username}
        onChange={(e) => setFormData({...formData, username: e.target.value})}
        required
      />
      <input
        type="password"
        placeholder="Password"
        value={formData.password}
        onChange={(e) => setFormData({...formData, password: e.target.value})}
        required
      />
      <select
        value={formData.role}
        onChange={(e) => setFormData({...formData, role: e.target.value})}
      >
        <option value="user">User</option>
        <option value="supervisor">Supervisor</option>
        <option value="admin">Admin</option>
      </select>
      <button type="submit" disabled={loading}>
        {loading ? 'Creating...' : 'Create User'}
      </button>
    </form>
  );
};
```

### 5. Create Tenant Page Implementation

```javascript
// Create new tenant
const createTenant = async (tenantData) => {
  try {
    const token = localStorage.getItem('access_token');
    const response = await fetch('/create-tenent', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(tenantData)
    });

    if (response.ok) {
      const data = await response.json();
      return data;
    } else {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to create tenant');
    }
  } catch (error) {
    console.error('Create tenant error:', error);
    throw error;
  }
};

// Create tenant form component
const CreateTenantForm = () => {
  const [formData, setFormData] = useState({
    tenant_name: '',
    tenant_slug: '',
    product: '685d2c9bb23fc531edf6dd0c', // Default product ID
    username: 'admin'
  });
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const result = await createTenant(formData);
      
      // Show success message with invitation link
      alert(`Tenant created successfully! Invitation link: ${result.invitation_link}`);
      
      // Optionally redirect to the invitation link
      if (confirm('Redirect to invitation link?')) {
        window.open(result.invitation_link, '_blank');
      }
      
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Tenant Name"
        value={formData.tenant_name}
        onChange={(e) => setFormData({...formData, tenant_name: e.target.value})}
        required
      />
      <input
        type="text"
        placeholder="Tenant Slug"
        value={formData.tenant_slug}
        onChange={(e) => setFormData({...formData, tenant_slug: e.target.value})}
        required
      />
      <input
        type="text"
        placeholder="Username"
        value={formData.username}
        onChange={(e) => setFormData({...formData, username: e.target.value})}
        required
      />
      <button type="submit">Create Tenant</button>
    </form>
  );
};
```

### 5. Invitation Link Handling

```javascript
// Handle invitation link redirect
const handleInvitationLink = (invitationLink) => {
  // Option 1: Open in new tab
  window.open(invitationLink, '_blank');
  
  // Option 2: Redirect current window
  // window.location.href = invitationLink;
  
  // Option 3: Copy to clipboard
  navigator.clipboard.writeText(invitationLink).then(() => {
    alert('Invitation link copied to clipboard!');
  });
};

// Parse invitation link components
const parseInvitationLink = (link) => {
  const url = new URL(link);
  return {
    token: url.searchParams.get('token'),
    username: url.searchParams.get('username'),
    role: url.searchParams.get('role')
  };
};
```

---

## 🔒 Error Handling

### Common Error Responses

```javascript
// Generic error handler
const handleApiError = (response) => {
  switch (response.status) {
    case 401:
      // Redirect to login
      localStorage.removeItem('access_token');
      window.location.href = '/login';
      break;
    case 403:
      alert('You do not have permission to perform this action');
      break;
    case 404:
      alert('Resource not found');
      break;
    case 500:
      alert('Server error. Please try again later.');
      break;
    default:
      alert('An unexpected error occurred');
  }
};
```

### Token Refresh (if implemented)

```javascript
// Check if token is expired and refresh if needed
const ensureValidToken = async () => {
  const token = localStorage.getItem('access_token');
  if (!token) {
    throw new Error('No token found');
  }
  
  // Decode JWT to check expiration (you'll need a JWT library)
  const payload = JSON.parse(atob(token.split('.')[1]));
  const isExpired = payload.exp * 1000 < Date.now();
  
  if (isExpired) {
    // Redirect to login or refresh token
    localStorage.removeItem('access_token');
    window.location.href = '/login';
  }
  
  return token;
};
```

---

## � API Endpoints Summary

| Method | Endpoint | Description | Auth Required | Admin Only |
|--------|----------|-------------|---------------|------------|
| `POST` | `/auth/login` | Authenticate user and get access token | ❌ | ❌ |
| `GET` | `/verify_token` | Verify if current token is valid | ✅ | ❌ |
| `GET` | `/tenants/list` | Get list of all tenants | ✅ | ✅ |
| `GET` | `/tenants/{tenant_id}` | Get specific tenant details by ID | ✅ | ✅ |
| `POST` | `/create-tenent` | Create a new tenant | ✅ | ✅ |
| `POST` | `/create_user` | Create a new user in admin system | ✅ | ✅ |

### Request/Response Content Types

- **Login**: `application/x-www-form-urlencoded` → `application/json`
- **All other endpoints**: `application/json` → `application/json`

### Authentication Header Format
```
Authorization: Bearer <access_token>
```

### Common HTTP Status Codes
- `200`: Success
- `400`: Bad Request (validation errors, invalid data)
- `401`: Unauthorized (invalid/missing token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found (resource doesn't exist)
- `500`: Internal Server Error

---

## �📋 Frontend Checklist

### Login Screen
- [ ] Username/password input fields
- [ ] Client ID (tenant slug) input field
- [ ] Login button with loading state
- [ ] Error message display
- [ ] Token storage after successful login

### Tenant List Page
- [ ] Display list of tenants with name, slug, created date
- [ ] Click handler for tenant selection
- [ ] Loading state while fetching
- [ ] Error handling for failed requests
- [ ] Admin-only access control

### Tenant Selection
- [ ] Fetch and display tenant details
- [ ] Store selected tenant information
- [ ] Navigate to tenant-specific dashboard
- [ ] Handle selection errors

### Create Tenant Page
- [ ] Form with tenant name, slug, username fields
- [ ] Form validation
- [ ] Submit button with loading state
- [ ] Success message with invitation link
- [ ] Option to redirect to invitation link
- [ ] Error handling and display

### General
- [ ] Consistent error handling across all pages
- [ ] Token expiration handling
- [ ] Loading states for all API calls
- [ ] Responsive design for mobile devices
- [ ] Proper navigation between pages
