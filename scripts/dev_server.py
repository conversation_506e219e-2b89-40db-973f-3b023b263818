#!/usr/bin/env python3
"""
Development server script for Multi-Tenant System Backend

This script provides a convenient way to start the development server
with proper configuration and hot-reload enabled.
"""

import os
import sys
import uvicorn
from pathlib import Path

def main():
    """Start the development server"""
    # Add the project root to Python path
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))
    
    # Set development environment
    os.environ.setdefault("ENVIRONMENT", "development")
    
    # Configure uvicorn for development
    config = {
        "app": "main:app",
        "host": "0.0.0.0",
        "port": 8000,
        "reload": True,
        "reload_dirs": [str(project_root / "app")],
        "log_level": "info",
        "access_log": True,
    }
    
    print("🚀 Starting Multi-Tenant System Backend Development Server")
    print(f"📁 Project root: {project_root}")
    print(f"🌐 Server will be available at: http://localhost:{config['port']}")
    print(f"📚 API documentation: http://localhost:{config['port']}/docs")
    print("🔄 Hot-reload enabled")
    print("-" * 60)
    
    try:
        uvicorn.run(**config)
    except KeyboardInterrupt:
        print("\n👋 Development server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
