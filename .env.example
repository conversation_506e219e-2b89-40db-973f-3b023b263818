# MongoDB Configuration
MONGO_URI=********************************:port/database
# Example: MONGO_URI=*******************************************/
# Example: MONGO_URI=mongodb+srv://username:<EMAIL>/

# Database Configuration
DATABASE_NAME=multi_tenant_admin

# Application Configuration
PYTHONPATH=/app

# Optional: JWT Configuration (if stored in env)
# SECRET_KEY=your-secret-key-here
# ALGORITHM=HS256

# Optional: CORS Configuration (if stored in env)
# ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# Optional: Other service configurations
# REDIS_URL=redis://localhost:6379
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
