"""
Multi-Tenant System Backend

A FastAPI-based multi-tenant management system that provides:
- User authentication and authorization
- Tenant creation and management
- Role-based access control
- MongoDB integration
- JWT token authentication
"""

__version__ = "0.1.0"
__author__ = "NextAI Team"
__email__ = "<EMAIL>"
__description__ = "Multi-Tenant System Backend - FastAPI-based tenant management system"

# Package metadata
__all__ = [
    "__version__",
    "__author__", 
    "__email__",
    "__description__",
]
