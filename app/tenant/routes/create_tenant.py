
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import Optional, List
from bson import ObjectId
from datetime import datetime
from app.core.database import get_async_client, get_admin_db
from app.core.permissions import require_admin
from app.models.user import UserTenantDB
from app.tenant.module.create_tenent import Tenant, TenantExistsError


tenant_router = APIRouter(
    tags=["Tenants"],
)

class TenantCreate(BaseModel):
    tenant_name: str
    tenant_slug: str
    product: str
    username: str = "admin"

class TenantCreateResponse(BaseModel):
    success: bool
    message: str
    tenant_id: Optional[str] = None
    tenant_slug: Optional[str] = None
    database_name: Optional[str] = None
    invitation_link: Optional[str] = None

class TenantInfo(BaseModel):
    id: str
    name: str
    slug: str
    database_name: str
    created_at: datetime=datetime.now()

class TenantListResponse(BaseModel):
    success: bool
    tenants: List[TenantInfo]
    total: int

class TenantDetailResponse(BaseModel):
    success: bool
    tenant: TenantInfo

@tenant_router.post("/create-tenent", response_model=TenantCreateResponse)
async def create_tenant(
    request: TenantCreate = Depends(),
    current_user: UserTenantDB = Depends(require_admin())
):
    """
    Create a new tenant with all necessary configurations and default data.

    Args:
        request: TenantCreate model containing tenant details

    Returns:
        TenantCreateResponse with success status and tenant information
    """
    try:
        # Validate input
        if not request.tenant_name or not request.tenant_slug:
            raise HTTPException(
                status_code=400,
                detail="Tenant name and slug are required"
            )

        # Get admin database

        # Get product database information
        try:
            tenant_admin_doc= await current_user.db.tenants.find_one(
                {"_id": ObjectId(request.product)},
                {"database_name": 1}
            )
            if not tenant_admin_doc:
                raise HTTPException(
                    status_code=404,
                    detail=f"Product with ID {request.product} not found"
                )

            database_name = tenant_admin_doc.get("database_name")
            if not database_name:
                raise HTTPException(
                    status_code=500,
                    detail="Missing database_name in product record"
                )
        except Exception as e:
            if "invalid ObjectId" in str(e).lower():
                raise HTTPException(
                    status_code=400,
                    detail="Invalid product ID format"
                )
            raise

        # Get product database
        product_db = current_user.db_client[database_name]

        # Get requirements for new tenant
        requirements = await product_db.requirements.find_one(
            {"name": "new_tenant_requirement"}
        )
        if not requirements:
            raise HTTPException(
                status_code=500,
                detail="New tenant requirements not found in product database"
            )

        # Create tenant instance with current user context
        tenant = Tenant(
            name=request.tenant_name,
            slug=request.tenant_slug,
            username=request.username,
            requirements=requirements,
            product_db=product_db,
            current_user=current_user,
            db_client=current_user.db_client
        )

        # Execute tenant creation steps
        tenant_id = await tenant._register_tenant()
        database_name = await tenant._prepare_client_database()
        invitation_link = await tenant._insert_default_data()

        return TenantCreateResponse(
            success=True,
            message="Tenant created successfully",
            tenant_id=tenant_id,
            tenant_slug=request.tenant_slug,
            database_name=database_name,
            invitation_link=invitation_link
        )

    except TenantExistsError as e:
        # Return 201 status code for existing tenant (as requested)
        raise HTTPException(
            status_code=status.HTTP_201_CREATED,
            detail=e.message
        )
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        # Log the error and return a generic error response
        print(f"Error creating tenant: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create tenant: {str(e)}"
        )



@tenant_router.get("/tenants/list", response_model=TenantListResponse)
async def list_tenants(current_user: UserTenantDB = Depends(require_admin())):
    """
    List all tenants in the system.
    Only admins can view all tenants.
    """
    try:
        cursor = current_user.db.tenants.find({}, {
            "name": 1,
            "slug": 1,
            "database_name": 1,
            "_id": 1
        })
        tenants_data = await  cursor.to_list(length=None)

        # Format tenants according to TenantInfo model
        tenants = [
            TenantInfo(
                id=str(tenant["_id"]),
                name=tenant.get("name", "Unknown"),
                slug=tenant.get("slug", "unknown"),
                database_name=tenant.get("database_name", "unknown_db")
            )
            for tenant in tenants_data
        ]

        return TenantListResponse(
            success=True,
            tenants=tenants,
            total=len(tenants)
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching tenants: {str(e)}"
        )


@tenant_router.get("/tenants/{tenant_id}", response_model=TenantDetailResponse)
async def get_tenant_by_id(
    tenant_id: str,
    current_user: UserTenantDB = Depends(require_admin())
):
    """
    Get specific tenant details by ID.
    Only admins can view tenant details.
    """
    try:

        # Validate ObjectId format
        try:
            object_id = ObjectId(tenant_id)
        except Exception:
            raise HTTPException(
                status_code=400,
                detail="Invalid tenant ID format"
            )

        tenant_data = await current_user.db.tenants.find_one(
            {"_id": object_id},
            {
                "name": 1,
                "slug": 1,
                "database_name": 1,
                "_id": 1
            }
        )

        if not tenant_data:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        tenant = TenantInfo(
            id=str(tenant_data["_id"]),
            name=tenant_data.get("name", "Unknown"),
            slug=tenant_data.get("slug", "unknown"),
            database_name=tenant_data.get("database_name", "unknown_db")
        )

        return TenantDetailResponse(
            success=True,
            tenant=tenant
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching tenant: {str(e)}"
        )
